# coding:gbk
"""
格力电器股价查询 - 测试版
专门用于测试获取历史数据，演示如何获取指定时间点股价
"""

def init(ContextInfo):
    """初始化"""
    print("格力电器股价查询测试版启动")
    print("将获取最近交易日的9:35, 9:40, 9:45时间点股价")

def handlebar(ContextInfo):
    """主执行函数"""
    
    stock_code = "000651.SZ"  # 格力电器
    
    print(f"\n开始查询 {stock_code} (格力电器) 的股价数据...")
    
    try:
        # 获取最近的分钟数据
        print("\n=== 获取最近分钟数据 ===")
        data = ContextInfo.get_market_data_ex(
            fields=['open', 'high', 'low', 'close', 'volume', 'amount'],
            stock_code=[stock_code],
            period='1m',
            count=200,  # 获取最近200条分钟数据
            subscribe=False
        )
        
        if stock_code in data and not data[stock_code].empty:
            df = data[stock_code]
            print(f"成功获取 {len(df)} 条分钟数据")
            
            # 显示数据时间范围
            print(f"数据时间范围: {df.index[0]} 到 {df.index[-1]}")
            
            # 获取最近交易日
            latest_date = df.index[-1][:8]  # 提取YYYYMMDD
            print(f"最近交易日: {latest_date}")
            
            # 筛选最近交易日的数据
            today_data = df[df.index.str.startswith(latest_date)]
            
            if not today_data.empty:
                print(f"最近交易日共有 {len(today_data)} 条分钟数据")
                
                # 查找指定时间点
                find_target_times(today_data, latest_date)
            else:
                print("未找到最近交易日的分钟数据")
                
        else:
            print("未获取到数据，可能原因：")
            print("1. 网络连接问题")
            print("2. 股票代码错误")
            print("3. 需要下载历史数据")
            
        # 获取日线数据作为参考
        print("\n=== 获取日线数据参考 ===")
        daily_data = ContextInfo.get_market_data_ex(
            fields=['open', 'high', 'low', 'close', 'volume'],
            stock_code=[stock_code],
            period='1d',
            count=3,
            subscribe=False
        )
        
        if stock_code in daily_data and not daily_data[stock_code].empty:
            df_daily = daily_data[stock_code]
            print("最近3个交易日收盘价：")
            for date, row in df_daily.iterrows():
                print(f"{date}: {row['close']:.2f} 元")
        
    except Exception as e:
        print(f"查询出错: {str(e)}")

def find_target_times(df, date_str):
    """查找目标时间点的股价"""
    
    target_times = ["0935", "0940", "0945"]
    
    print(f"\n格力电器 {date_str} 指定时间点股价：")
    print("=" * 60)
    
    for time_str in target_times:
        found_data = None
        
        # 尝试不同的匹配方式
        for suffix in ["", "00", "30"]:
            pattern = time_str + suffix
            matches = df[df.index.str.contains(pattern)]
            if not matches.empty:
                found_data = matches.iloc[0]
                actual_time = matches.index[0]
                break
        
        if found_data is not None:
            print(f"时间: {time_str[:2]}:{time_str[2:]} (实际: {actual_time[-6:]})")
            print(f"  股价: {found_data['close']:.2f} 元")
            print(f"  开盘: {found_data['open']:.2f} 元")
            print(f"  最高: {found_data['high']:.2f} 元")
            print(f"  最低: {found_data['low']:.2f} 元")
            print(f"  成交量: {found_data['volume']:,.0f}")
            print(f"  成交额: {found_data['amount']:,.0f}")
            print("-" * 30)
        else:
            print(f"时间: {time_str[:2]}:{time_str[2:]} - 未找到数据")
    
    # 显示可用时间点示例
    print("\n可用时间点示例（前15个）：")
    for i, time_point in enumerate(df.head(15).index):
        time_part = time_point[-6:]  # 提取HHMMSS部分
        formatted_time = f"{time_part[:2]}:{time_part[2:4]}:{time_part[4:]}"
        print(f"{i+1:2d}. {formatted_time} - 股价: {df.iloc[i]['close']:.2f}")

"""
使用说明：

这是一个测试版本，用于：
1. 验证QMT连接是否正常
2. 测试数据获取功能
3. 演示如何查找指定时间点的股价

如果运行成功，说明：
- QMT连接正常
- 可以获取格力电器的数据
- 时间匹配逻辑正确

如果要查询2025年7月30日的数据：
1. 等到2025年7月30日当天
2. 确保已下载当天的分钟数据
3. 将代码中的count参数调整为获取当天数据

注意：
- 000651.SZ 是格力电器在深交所的代码
- 分钟数据只在交易时间内产生
- 9:35, 9:40, 9:45 都在上午交易时间内(9:30-11:30)
"""
