# coding:gbk
"""
格力电器20250730专用版
专门获取2025年7月30日 9:35、9:40、9:45 三个时间点的股价
只保存这三个时间点的数据到CSV文件
"""

import pandas as pd
from datetime import datetime

def init(ContextInfo):
    """初始化"""
    print("=" * 60)
    print("格力电器 2025年7月30日 专用查询")
    print("目标时间点：9:35、9:40、9:45")
    print("只保存这三个时间点的股价数据")
    print("=" * 60)

def handlebar(ContextInfo):
    """主执行函数"""
    
    stock_code = "000651.SZ"  # 格力电器
    target_date = "20250730"  # 2025年7月30日
    target_times = ["0935", "0940", "0945"]  # 目标时间点
    
    print(f"\n正在查询 {stock_code} 在 {target_date} 的股价数据...")
    
    try:
        # 获取2025年7月30日的分钟数据
        minute_data = ContextInfo.get_market_data_ex(
            fields=['open', 'high', 'low', 'close', 'volume', 'amount'],
            stock_code=[stock_code],
            period='1m',
            start_time=target_date,
            end_time=target_date,
            count=-1,
            subscribe=False
        )
        
        if stock_code in minute_data and not minute_data[stock_code].empty:
            df = minute_data[stock_code]
            print(f"✅ 成功获取 {target_date} 的 {len(df)} 条分钟数据")
            
            # 查找并保存目标时间点数据
            target_data_list = []
            
            print(f"\n查找目标时间点数据：")
            print("-" * 50)
            
            for time_str in target_times:
                matching_data = find_time_data(df, time_str)
                
                if matching_data is not None:
                    row = matching_data.iloc[0]
                    actual_time = matching_data.index[0]
                    
                    # 添加到结果列表
                    target_data_list.append({
                        '股票代码': stock_code,
                        '股票名称': '格力电器',
                        '日期': target_date,
                        '目标时间': f"{time_str[:2]}:{time_str[2:]}",
                        '实际时间': actual_time,
                        '开盘价': round(row['open'], 2),
                        '最高价': round(row['high'], 2),
                        '最低价': round(row['low'], 2),
                        '收盘价': round(row['close'], 2),
                        '成交量': int(row['volume']),
                        '成交额': round(row['amount'], 2)
                    })
                    
                    print(f"✅ {time_str[:2]}:{time_str[2:]} - 股价: {row['close']:.2f} 元")
                else:
                    print(f"❌ {time_str[:2]}:{time_str[2:]} - 未找到数据")
            
            # 保存到CSV文件
            if target_data_list:
                filename = save_target_data_csv(target_data_list, target_date)
                
                print(f"\n" + "=" * 60)
                print(f"✅ 成功保存 {len(target_data_list)} 个时间点的数据")
                print(f"📁 文件名: {filename}")
                print("=" * 60)
                
                # 显示保存的数据
                print("\n保存的数据内容：")
                for data in target_data_list:
                    print(f"{data['目标时间']}: {data['收盘价']} 元")
            else:
                print(f"\n❌ 未找到任何目标时间点的数据")
                print("可能原因：")
                print("1. 该时间段没有交易数据")
                print("2. 数据格式不匹配")
                
        else:
            print(f"\n❌ 未获取到 {target_date} 的数据")
            print("可能原因：")
            print("1. 该日期是未来日期，数据尚未产生")
            print("2. 该日期是非交易日（周末或节假日）")
            print("3. 需要先在QMT中下载历史数据")
            print("4. 网络连接问题")
            
            print(f"\n💡 解决方案：")
            print("1. 确保今天是2025年7月30日或之后")
            print("2. 在QMT中下载000651.SZ的1分钟历史数据")
            print("3. 确保数据时间范围包含20250730")
            print("4. 检查网络连接和QMT登录状态")
            
    except Exception as e:
        print(f"❌ 查询出错: {str(e)}")
        print("\n请检查：")
        print("1. QMT是否已连接行情服务器")
        print("2. 网络连接是否正常")
        print("3. 股票代码是否正确")

def find_time_data(df, time_str):
    """查找指定时间的数据"""
    # 尝试多种时间格式匹配
    patterns = [
        time_str,           # 0935
        f"{time_str}00",    # 093500
        f"{time_str}30",    # 093530
        f"{time_str}15",    # 093515
        f"{time_str}45"     # 093545
    ]
    
    for pattern in patterns:
        matches = df[df.index.str.contains(pattern)]
        if not matches.empty:
            return matches
    
    return None

def save_target_data_csv(data_list, date_str):
    """保存目标时间点数据到CSV文件"""
    try:
        # 创建DataFrame
        df_save = pd.DataFrame(data_list)
        
        # 生成文件名（只包含日期，不包含时间戳，避免重复文件）
        filename = f"格力电器_{date_str}_目标时间点.csv"
        
        # 保存到CSV文件
        df_save.to_csv(filename, index=False, encoding='utf-8-sig')
        
        return filename
        
    except Exception as e:
        print(f"❌ 保存CSV文件失败: {str(e)}")
        return None

"""
专用说明：

这个程序专门用于获取格力电器在2025年7月30日的三个时间点股价：
- 9:35
- 9:40
- 9:45

生成的CSV文件：
- 文件名：格力电器_20250730_目标时间点.csv
- 内容：只包含这三个时间点的股价数据

CSV文件格式：
股票代码,股票名称,日期,目标时间,实际时间,开盘价,最高价,最低价,收盘价,成交量,成交额
000651.SZ,格力电器,20250730,9:35,20250730093500,XX.XX,XX.XX,XX.XX,XX.XX,XXXXX,XXXXXXX.XX
000651.SZ,格力电器,20250730,9:40,20250730094000,XX.XX,XX.XX,XX.XX,XX.XX,XXXXX,XXXXXXX.XX
000651.SZ,格力电器,20250730,9:45,20250730094500,XX.XX,XX.XX,XX.XX,XX.XX,XXXXX,XXXXXXX.XX

使用时机：
- 在2025年7月30日当天或之后运行
- 确保已下载相应的历史数据

注意事项：
- 如果是未来日期，程序会提示无法获取数据
- 确保QMT已连接并下载了相应的历史数据
- CSV文件会保存在QMT的运行目录下
"""
