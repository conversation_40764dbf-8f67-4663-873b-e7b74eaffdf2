# coding:gbk
"""
格力电器股价查询策略 - 修正版
目标：获取格力电器在指定时间点的股价
时间点：9:35, 9:40, 9:45
注意：由于20250730是未来日期，代码会获取最近交易日数据进行演示
"""

def init(ContextInfo):
    """初始化函数"""
    print("=" * 50)
    print("格力电器股价查询策略启动")
    print("目标查询时间：9:35, 9:40, 9:45")
    print("注意：20250730是未来日期，将获取最近交易日数据演示")
    print("=" * 50)

def handlebar(ContextInfo):
    """主执行函数"""
    
    # 格力电器股票代码
    stock_code = "000651.SZ"
    target_date = "20250730"  # 目标日期（未来）
    
    print(f"\n正在查询 {stock_code} 的股价数据...")
    
    try:
        # 步骤1：尝试获取目标日期数据
        print(f"\n=== 步骤1：尝试获取 {target_date} 的数据 ===")
        target_data_found = get_target_date_data(ContextInfo, stock_code, target_date)
        
        # 步骤2：如果目标日期无数据，获取最近交易日数据
        if not target_data_found:
            print(f"未获取到 {target_date} 的数据（该日期为未来日期）")
            print("\n=== 步骤2：获取最近交易日数据进行演示 ===")
            get_recent_trading_data(ContextInfo, stock_code)
        
        # 步骤3：获取最新日线数据作为参考
        print("\n=== 步骤3：获取最新日线数据作为参考 ===")
        get_latest_daily_data(ContextInfo, stock_code)
                
    except Exception as e:
        print(f"查询出错: {str(e)}")
        print("\n可能的解决方案：")
        print("1. 确保QMT已连接行情服务器")
        print("2. 下载格力电器的历史数据")
        print("3. 检查网络连接")

def get_target_date_data(ContextInfo, stock_code, target_date):
    """获取目标日期的数据"""
    try:
        minute_data = ContextInfo.get_market_data_ex(
            fields=['open', 'high', 'low', 'close', 'volume', 'amount'],
            stock_code=[stock_code],
            period='1m',
            start_time=target_date,
            end_time=target_date,
            count=-1,
            subscribe=False
        )
        
        if stock_code in minute_data and not minute_data[stock_code].empty:
            df = minute_data[stock_code]
            if len(df) > 0:
                print(f"成功获取目标日期 {len(df)} 条分钟数据")
                process_minute_data(df, target_date)
                return True
        
        return False
    except Exception as e:
        print(f"获取目标日期数据出错: {str(e)}")
        return False

def get_recent_trading_data(ContextInfo, stock_code):
    """获取最近交易日的分钟数据"""
    try:
        recent_minute_data = ContextInfo.get_market_data_ex(
            fields=['open', 'high', 'low', 'close', 'volume', 'amount'],
            stock_code=[stock_code],
            period='1m',
            count=300,  # 获取最近300条分钟数据（约2个交易日）
            subscribe=False
        )
        
        if stock_code in recent_minute_data and not recent_minute_data[stock_code].empty:
            df_recent = recent_minute_data[stock_code]
            print(f"获取到最近 {len(df_recent)} 条分钟数据")
            
            # 找到最近的交易日
            latest_date = df_recent.index[-1][:8]  # 提取日期部分YYYYMMDD
            print(f"最近交易日: {latest_date}")
            
            # 筛选最近交易日的数据
            latest_day_data = df_recent[df_recent.index.str.startswith(latest_date)]
            if not latest_day_data.empty:
                process_minute_data(latest_day_data, latest_date)
            else:
                print("未找到最近交易日的分钟数据")
        else:
            print("未获取到任何分钟数据")
    except Exception as e:
        print(f"获取最近交易日数据出错: {str(e)}")

def get_latest_daily_data(ContextInfo, stock_code):
    """获取最新日线数据作为参考"""
    try:
        latest_data = ContextInfo.get_market_data_ex(
            fields=['open', 'high', 'low', 'close', 'volume', 'amount'],
            stock_code=[stock_code],
            period='1d',
            count=5,  # 获取最近5天
            subscribe=False
        )
        
        if stock_code in latest_data and not latest_data[stock_code].empty:
            df_latest = latest_data[stock_code]
            print("格力电器最近交易日数据：")
            for idx, row in df_latest.tail(3).iterrows():
                print(f"{idx}: 收盘价 {row['close']:.2f} 元, 成交量 {row['volume']:,.0f} 股")
        else:
            print("未获取到最新日线数据")
    except Exception as e:
        print(f"获取最新日线数据出错: {str(e)}")

def process_minute_data(df, date_str):
    """处理分钟数据，查找指定时间点"""
    
    # 查找目标时间点
    target_times = ["0935", "0940", "0945"]  # HHMM格式
    
    print(f"\n格力电器 {date_str} 指定时间点股价：")
    print("=" * 70)
    
    found_any_data = False
    
    for time_str in target_times:
        # 查找匹配的时间 - 改进匹配逻辑
        matching_data = find_matching_time_data(df, time_str)
        
        if matching_data is not None and not matching_data.empty:
            row = matching_data.iloc[0]
            actual_time = matching_data.index[0]
            found_any_data = True
            
            print(f"时间: {time_str[:2]}:{time_str[2:]} (实际时间: {actual_time})")
            print(f"  股价(收盘): {row['close']:.2f} 元")
            print(f"  开盘价:     {row['open']:.2f} 元")
            print(f"  最高价:     {row['high']:.2f} 元")
            print(f"  最低价:     {row['low']:.2f} 元")
            print(f"  成交量:     {row['volume']:,.0f} 股")
            print(f"  成交额:     {row['amount']:,.0f} 元")
            print("-" * 40)
        else:
            print(f"时间: {time_str[:2]}:{time_str[2:]} - 无数据")
    
    if not found_any_data:
        print("未找到指定时间点的数据")
        print("\n可用的时间点示例（前10个）：")
        available_times = df.head(10).index.tolist()
        for i, time_point in enumerate(available_times):
            print(f"{i+1}. {time_point}")

def find_matching_time_data(df, time_str):
    """查找匹配时间的数据"""
    # 尝试多种匹配方式
    patterns = [
        time_str,           # 0935
        f"{time_str}00",    # 093500
        f"{time_str}30",    # 093530
        time_str[:3]        # 093 (匹配093X)
    ]
    
    for pattern in patterns:
        temp_data = df[df.index.str.contains(pattern)]
        if not temp_data.empty:
            return temp_data
    
    return None

# 数据下载辅助函数（可选使用）
def download_data_helper():
    """下载历史数据的辅助函数"""
    try:
        # 注意：这个函数需要在QMT环境中才能使用
        download_history_data("000651.SZ", "1m", "20240801", "20240805")
        print("历史数据下载完成")
    except Exception as e:
        print(f"数据下载失败: {str(e)}")
        print("请手动在QMT中下载历史数据")

"""
使用说明：

1. 在QMT中创建新的Python策略
2. 复制此代码到策略编辑器
3. 点击"运行"执行查询

重要提醒：
- 000651.SZ 是格力电器的深交所股票代码
- 2025年7月30日是未来日期，目前无法获取实际数据
- 代码会自动获取最近交易日的数据进行演示
- 如果要查询历史日期，请将target_date改为历史日期，如"20240801"

如果没有数据显示：
1. 在QMT菜单中选择"数据管理"
2. 选择"股票数据下载"
3. 输入000651.SZ，选择1分钟数据
4. 设置时间范围包含目标日期
5. 点击下载

交易时间：
- 上午：9:30-11:30
- 下午：13:00-15:00
- 查询的9:35, 9:40, 9:45都在交易时间内
"""
