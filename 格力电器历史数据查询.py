# coding:gbk
"""
格力电器历史数据查询
获取2025年4月30日到2025年8月4日期间每个交易日的 9:35、9:40、9:45 三个时间点的股价
保存所有数据到CSV文件
"""

import pandas as pd
from datetime import datetime

def init(ContextInfo):
    """初始化"""
    print("=" * 70)
    print("格力电器历史数据查询")
    print("时间范围：2025年4月30日 到 2025年8月4日")
    print("目标时间点：每个交易日的 9:35、9:40、9:45")
    print("功能：获取并保存所有目标时间点的股价数据")
    print("=" * 70)

def handlebar(ContextInfo):
    """主执行函数"""
    
    stock_code = "000651.SZ"  # 格力电器
    start_date = "20250430"   # 开始日期：2025年4月30日
    end_date = "20250804"     # 结束日期：2025年8月4日
    target_times = ["0935", "0940", "0945"]  # 目标时间点
    
    print(f"\n🔍 开始查询 {stock_code} (格力电器)")
    print(f"📅 时间范围: {start_date} 到 {end_date}")
    print(f"⏰ 目标时间: {', '.join([f'{t[:2]}:{t[2:]}' for t in target_times])}")
    
    try:
        # 获取指定时间范围的分钟数据
        print(f"\n📡 正在获取分钟数据...")
        minute_data = ContextInfo.get_market_data_ex(
            fields=['open', 'high', 'low', 'close', 'volume', 'amount'],
            stock_code=[stock_code],
            period='1m',
            start_time=start_date,
            end_time=end_date,
            count=-1,
            subscribe=False
        )
        
        if stock_code in minute_data and not minute_data[stock_code].empty:
            df = minute_data[stock_code]
            print(f"✅ 成功获取 {len(df):,} 条分钟数据")
            
            # 获取数据的实际日期范围
            first_date = df.index[0][:8]
            last_date = df.index[-1][:8]
            print(f"📊 实际数据范围: {first_date} 到 {last_date}")
            
            # 按日期分组处理数据
            all_target_data = []
            processed_dates = set()
            
            print(f"\n🔄 开始处理各交易日数据...")
            print("=" * 70)
            
            # 遍历所有数据，按日期分组
            for index in df.index:
                current_date = index[:8]  # 提取日期 YYYYMMDD
                
                # 如果这个日期还没处理过
                if current_date not in processed_dates:
                    processed_dates.add(current_date)
                    
                    # 获取当天的所有数据
                    day_data = df[df.index.str.startswith(current_date)]
                    
                    if not day_data.empty:
                        print(f"\n📅 {current_date} ({len(day_data)} 条分钟数据)")
                        
                        # 查找当天的目标时间点
                        day_target_data = process_day_data(day_data, current_date, target_times, stock_code)
                        all_target_data.extend(day_target_data)
            
            # 保存所有数据到CSV文件
            if all_target_data:
                filename = save_all_data_csv(all_target_data, start_date, end_date)
                
                print(f"\n" + "=" * 70)
                print(f"🎉 数据处理完成！")
                print(f"📊 总共获取: {len(all_target_data)} 个时间点数据")
                print(f"📁 保存文件: {filename}")
                print(f"📅 涵盖交易日: {len(processed_dates)} 天")
                print("=" * 70)
                
                # 显示详细统计信息
                show_statistics(all_target_data)
                
            else:
                print(f"\n❌ 未找到任何目标时间点的数据")
                print("可能原因：")
                print("• 指定时间范围内没有交易数据")
                print("• 时间范围都是非交易日")
                print("• 数据格式不匹配")
                
        else:
            print(f"\n❌ 未获取到指定时间范围的数据")
            print("可能原因：")
            print("• 时间范围包含未来日期")
            print("• 需要先下载历史数据")
            print("• 网络连接问题")
            print("• 股票代码错误")
            
            print(f"\n💡 解决方案：")
            print("1. 在QMT中下载000651.SZ的1分钟历史数据")
            print("2. 确保数据时间范围包含20250430-20250804")
            print("3. 检查网络连接和QMT登录状态")
            print("4. 确认股票代码正确")
            
    except Exception as e:
        print(f"❌ 查询出错: {str(e)}")
        print("\n🔧 故障排除：")
        print("1. 检查QMT是否已连接行情服务器")
        print("2. 确认网络连接正常")
        print("3. 验证股票代码和日期格式")
        print("4. 重启QMT后重试")

def process_day_data(day_data, date_str, target_times, stock_code):
    """处理单日数据，查找目标时间点"""
    day_results = []
    
    for time_str in target_times:
        matching_data = find_time_data(day_data, time_str)
        
        if matching_data is not None:
            row = matching_data.iloc[0]
            actual_time = matching_data.index[0]
            
            # 添加到结果列表
            day_results.append({
                '股票代码': stock_code,
                '股票名称': '格力电器',
                '日期': date_str,
                '目标时间': f"{time_str[:2]}:{time_str[2:]}",
                '实际时间': actual_time,
                '开盘价': round(row['open'], 2),
                '最高价': round(row['high'], 2),
                '最低价': round(row['low'], 2),
                '收盘价': round(row['close'], 2),
                '成交量': int(row['volume']),
                '成交额': round(row['amount'], 2)
            })
            
            print(f"  ✅ {time_str[:2]}:{time_str[2:]} → {row['close']:.2f}元")
        else:
            print(f"  ❌ {time_str[:2]}:{time_str[2:]} → 无数据")
    
    return day_results

def find_time_data(df, time_str):
    """查找指定时间的数据，支持多种时间格式"""
    patterns = [
        time_str,           # 0935
        f"{time_str}00",    # 093500
        f"{time_str}30",    # 093530
        f"{time_str}15",    # 093515
        f"{time_str}45"     # 093545
    ]
    
    for pattern in patterns:
        matches = df[df.index.str.contains(pattern)]
        if not matches.empty:
            return matches
    
    return None

def save_all_data_csv(data_list, start_date, end_date):
    """保存所有数据到CSV文件"""
    try:
        # 创建DataFrame并排序
        df_save = pd.DataFrame(data_list)
        df_save = df_save.sort_values(['日期', '目标时间'])
        
        # 生成文件名
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"格力电器_{start_date}到{end_date}_目标时间点_{timestamp}.csv"
        
        # 保存到CSV文件
        df_save.to_csv(filename, index=False, encoding='utf-8-sig')
        
        return filename
        
    except Exception as e:
        print(f"❌ 保存CSV文件失败: {str(e)}")
        return None

def show_statistics(data_list):
    """显示详细统计信息"""
    try:
        df = pd.DataFrame(data_list)
        
        print(f"\n📈 详细统计信息")
        print("=" * 50)
        
        # 基本统计
        print(f"📊 数据概况:")
        print(f"   总记录数: {len(df):,} 条")
        print(f"   交易日数: {df['日期'].nunique()} 天")
        print(f"   日期范围: {df['日期'].min()} ~ {df['日期'].max()}")
        
        # 按时间点统计
        print(f"\n⏰ 各时间点数据分布:")
        time_counts = df['目标时间'].value_counts().sort_index()
        for time_point, count in time_counts.items():
            print(f"   {time_point}: {count:,} 条")
        
        # 股价统计
        print(f"\n💰 股价统计 (基于收盘价):")
        close_prices = df['收盘价']
        print(f"   最高价: {close_prices.max():.2f} 元")
        print(f"   最低价: {close_prices.min():.2f} 元")
        print(f"   平均价: {close_prices.mean():.2f} 元")
        print(f"   中位数: {close_prices.median():.2f} 元")
        print(f"   价格波动: {close_prices.max() - close_prices.min():.2f} 元")
        
        # 成交量统计
        print(f"\n📊 成交量统计:")
        volumes = df['成交量']
        print(f"   总成交量: {volumes.sum():,} 股")
        print(f"   平均成交量: {volumes.mean():,.0f} 股")
        print(f"   最大成交量: {volumes.max():,} 股")
        print(f"   最小成交量: {volumes.min():,} 股")
        
        # 成交额统计
        print(f"\n💵 成交额统计:")
        amounts = df['成交额']
        print(f"   总成交额: {amounts.sum():,.0f} 元")
        print(f"   平均成交额: {amounts.mean():,.0f} 元")
        
        # 按日期显示前几天和后几天的数据
        print(f"\n📅 数据样例 (前3天):")
        first_dates = df['日期'].unique()[:3]
        for date in first_dates:
            day_data = df[df['日期'] == date]
            print(f"   {date}: {len(day_data)} 条记录")
            for _, row in day_data.iterrows():
                print(f"     {row['目标时间']} → {row['收盘价']:.2f}元")
        
    except Exception as e:
        print(f"❌ 统计信息计算失败: {str(e)}")

"""
📋 程序使用说明

🎯 功能：
获取格力电器(000651.SZ)在2025年4月30日到8月4日期间，
每个交易日9:35、9:40、9:45三个时间点的股价数据。

📁 输出文件：
格力电器_20250430到20250804_目标时间点_时间戳.csv

📊 数据内容：
- 股票代码、股票名称
- 交易日期、目标时间、实际时间
- 开盘价、最高价、最低价、收盘价
- 成交量、成交额

🔧 使用步骤：
1. 在QMT中创建新的Python策略
2. 复制此代码到策略编辑器
3. 确保已下载000651.SZ的1分钟历史数据
4. 点击"运行"执行查询
5. 查看生成的CSV文件和统计信息

⚠️ 注意事项：
- 程序会自动跳过非交易日
- 如果包含未来日期，只获取已有的历史数据
- CSV文件使用UTF-8编码，支持中文
- 文件名包含时间戳，避免覆盖
"""
