# coding:gbk
"""
格力电器股价查询 - CSV保存版
功能：获取格力电器指定时间点股价并保存为CSV文件
时间点：9:35, 9:40, 9:45
"""

import pandas as pd
from datetime import datetime

def init(ContextInfo):
    """初始化"""
    print("=" * 60)
    print("格力电器股价查询 - CSV保存版")
    print("功能：获取指定时间点股价并保存为CSV文件")
    print("目标时间：9:35, 9:40, 9:45")
    print("=" * 60)

def handlebar(ContextInfo):
    """主执行函数"""
    
    stock_code = "000651.SZ"  # 格力电器
    target_date = "20250730"  # 目标日期
    
    print(f"\n开始查询 {stock_code} (格力电器) 的股价数据...")
    
    try:
        # 获取最近的分钟数据
        print("\n=== 获取分钟数据 ===")
        minute_data = ContextInfo.get_market_data_ex(
            fields=['open', 'high', 'low', 'close', 'volume', 'amount'],
            stock_code=[stock_code],
            period='1m',
            count=300,  # 获取最近300条分钟数据
            subscribe=False
        )
        
        if stock_code in minute_data and not minute_data[stock_code].empty:
            df_minute = minute_data[stock_code]
            print(f"成功获取 {len(df_minute)} 条分钟数据")
            
            # 获取最近交易日
            latest_date = df_minute.index[-1][:8]
            print(f"最近交易日: {latest_date}")
            
            # 筛选最近交易日的数据
            today_data = df_minute[df_minute.index.str.startswith(latest_date)]
            
            if not today_data.empty:
                # 处理并保存指定时间点数据
                target_data = process_and_save_target_times(today_data, latest_date)
                
                # 保存完整分钟数据
                save_full_minute_data(today_data, latest_date)
            else:
                print("未找到最近交易日的分钟数据")
        
        # 获取并保存日线数据
        print("\n=== 获取日线数据 ===")
        daily_data = ContextInfo.get_market_data_ex(
            fields=['open', 'high', 'low', 'close', 'volume', 'amount'],
            stock_code=[stock_code],
            period='1d',
            count=10,  # 获取最近10天
            subscribe=False
        )
        
        if stock_code in daily_data and not daily_data[stock_code].empty:
            df_daily = daily_data[stock_code]
            print(f"成功获取 {len(df_daily)} 条日线数据")
            save_daily_data(df_daily)
        
        print("\n" + "=" * 60)
        print("✅ 数据查询和保存完成！")
        print("📁 请查看当前目录下生成的CSV文件")
        print("=" * 60)
        
    except Exception as e:
        print(f"❌ 查询出错: {str(e)}")

def process_and_save_target_times(df, date_str):
    """处理并保存指定时间点的数据"""
    
    target_times = ["0935", "0940", "0945"]
    target_data_list = []
    
    print(f"\n格力电器 {date_str} 指定时间点股价：")
    print("=" * 70)
    
    for time_str in target_times:
        matching_data = find_matching_time(df, time_str)
        
        if matching_data is not None:
            row = matching_data.iloc[0]
            actual_time = matching_data.index[0]
            
            # 添加到数据列表
            target_data_list.append({
                '股票代码': '000651.SZ',
                '股票名称': '格力电器',
                '日期': date_str,
                '目标时间': f"{time_str[:2]}:{time_str[2:]}",
                '实际时间': actual_time,
                '开盘价': round(row['open'], 2),
                '最高价': round(row['high'], 2),
                '最低价': round(row['low'], 2),
                '收盘价': round(row['close'], 2),
                '成交量': int(row['volume']),
                '成交额': round(row['amount'], 2)
            })
            
            print(f"✅ {time_str[:2]}:{time_str[2:]} - 股价: {row['close']:.2f} 元")
        else:
            print(f"❌ {time_str[:2]}:{time_str[2:]} - 无数据")
    
    # 保存目标时间点数据
    if target_data_list:
        filename = save_target_times_csv(target_data_list, date_str)
        print(f"\n📁 目标时间点数据已保存: {filename}")
    
    return target_data_list

def find_matching_time(df, time_str):
    """查找匹配的时间数据"""
    patterns = [time_str, f"{time_str}00", f"{time_str}30"]
    
    for pattern in patterns:
        matches = df[df.index.str.contains(pattern)]
        if not matches.empty:
            return matches
    return None

def save_target_times_csv(data_list, date_str):
    """保存目标时间点数据为CSV"""
    try:
        df_save = pd.DataFrame(data_list)
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"格力电器_目标时间点_{date_str}_{timestamp}.csv"
        
        df_save.to_csv(filename, index=False, encoding='utf-8-sig')
        return filename
    except Exception as e:
        print(f"❌ 保存目标时间点数据失败: {str(e)}")
        return None

def save_full_minute_data(df, date_str):
    """保存完整分钟数据"""
    try:
        # 准备数据
        df_save = df.reset_index()
        df_save.rename(columns={'index': '时间'}, inplace=True)
        df_save.insert(0, '股票代码', '000651.SZ')
        df_save.insert(1, '股票名称', '格力电器')
        df_save.insert(2, '日期', date_str)
        
        # 数值格式化
        for col in ['open', 'high', 'low', 'close']:
            df_save[col] = df_save[col].round(2)
        df_save['volume'] = df_save['volume'].astype(int)
        df_save['amount'] = df_save['amount'].round(2)
        
        # 保存文件
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"格力电器_完整分钟数据_{date_str}_{timestamp}.csv"
        
        df_save.to_csv(filename, index=False, encoding='utf-8-sig')
        print(f"📁 完整分钟数据已保存: {filename} ({len(df_save)} 条记录)")
        
        return filename
    except Exception as e:
        print(f"❌ 保存完整分钟数据失败: {str(e)}")
        return None

def save_daily_data(df):
    """保存日线数据"""
    try:
        # 准备数据
        df_save = df.reset_index()
        df_save.rename(columns={'index': '日期'}, inplace=True)
        df_save.insert(0, '股票代码', '000651.SZ')
        df_save.insert(1, '股票名称', '格力电器')
        
        # 数值格式化
        for col in ['open', 'high', 'low', 'close']:
            df_save[col] = df_save[col].round(2)
        df_save['volume'] = df_save['volume'].astype(int)
        df_save['amount'] = df_save['amount'].round(2)
        
        # 保存文件
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"格力电器_日线数据_{timestamp}.csv"
        
        df_save.to_csv(filename, index=False, encoding='utf-8-sig')
        print(f"📁 日线数据已保存: {filename} ({len(df_save)} 条记录)")
        
        # 显示最近几天的数据
        print("\n最近交易日收盘价：")
        for _, row in df_save.tail(5).iterrows():
            print(f"  {row['日期']}: {row['close']:.2f} 元")
        
        return filename
    except Exception as e:
        print(f"❌ 保存日线数据失败: {str(e)}")
        return None

"""
使用说明：

1. 在QMT中创建新的Python策略
2. 复制此代码到策略编辑器
3. 点击"运行"执行查询
4. 查看生成的CSV文件

生成的CSV文件：
1. 格力电器_目标时间点_日期_时间戳.csv - 包含9:35,9:40,9:45三个时间点的数据
2. 格力电器_完整分钟数据_日期_时间戳.csv - 包含当天所有分钟数据
3. 格力电器_日线数据_时间戳.csv - 包含最近10天的日线数据

CSV文件特点：
- 使用UTF-8编码，支持中文
- 包含完整的股票信息（代码、名称、日期等）
- 数值已格式化（价格保留2位小数，成交量为整数）
- 文件名包含时间戳，避免覆盖

注意事项：
- CSV文件保存在QMT的运行目录下
- 如果目标日期是未来日期，会获取最近交易日的数据
- 确保已下载相应的历史数据
"""
