# coding:gbk
"""
简化版：获取格力电器20250730指定时间点股价
目标时间：9:35, 9:40, 9:45
"""

def init(ContextInfo):
    """初始化"""
    print("格力电器股价查询策略启动")
    print("查询日期：2025年7月30日")
    print("查询时间：9:35, 9:40, 9:45")

def handlebar(ContextInfo):
    """主执行函数"""
    
    # 格力电器股票代码
    stock_code = "000651.SZ"
    target_date = "20250730"
    
    print(f"\n正在查询 {stock_code} 在 {target_date} 的股价数据...")
    
    try:
        # 方法1：获取当日分钟数据
        print("\n=== 获取分钟级数据 ===")
        minute_data = ContextInfo.get_market_data_ex(
            fields=['open', 'high', 'low', 'close', 'volume', 'amount'],
            stock_code=[stock_code],
            period='1m',
            start_time=target_date,
            end_time=target_date,
            count=-1,
            subscribe=False
        )
        
        if stock_code in minute_data and not minute_data[stock_code].empty:
            df = minute_data[stock_code]
            print(f"成功获取 {len(df)} 条分钟数据")
            
            # 查找目标时间点
            target_times = ["093500", "094000", "094500"]
            
            print("\n格力电器指定时间点股价：")
            print("=" * 60)
            
            for time_str in target_times:
                # 查找匹配的时间
                time_pattern = time_str[:4]  # 取HHMM
                matching_data = df[df.index.str.contains(time_pattern)]
                
                if not matching_data.empty:
                    row = matching_data.iloc[0]
                    print(f"时间: {time_str[:2]}:{time_str[2:4]}")
                    print(f"  股价: {row['close']:.2f} 元")
                    print(f"  开盘: {row['open']:.2f} 元")
                    print(f"  最高: {row['high']:.2f} 元") 
                    print(f"  最低: {row['low']:.2f} 元")
                    print(f"  成交量: {row['volume']:,.0f} 股")
                    print(f"  成交额: {row['amount']:,.0f} 元")
                    print("-" * 30)
                else:
                    print(f"时间: {time_str[:2]}:{time_str[2:4]} - 无数据")
        else:
            print("未获取到数据，请检查：")
            print("1. 是否已下载历史数据")
            print("2. 日期是否正确（注意：2025年7月30日是未来日期）")
            print("3. 股票代码是否正确")
            
        # 方法2：获取最新可用数据作为参考
        print("\n=== 获取最新数据作为参考 ===")
        latest_data = ContextInfo.get_market_data_ex(
            fields=['open', 'high', 'low', 'close', 'volume', 'amount'],
            stock_code=[stock_code],
            period='1d',
            count=5,  # 获取最近5天
            subscribe=False
        )
        
        if stock_code in latest_data and not latest_data[stock_code].empty:
            df_latest = latest_data[stock_code]
            print("格力电器最近交易日数据：")
            for idx, row in df_latest.tail(3).iterrows():
                print(f"{idx}: 收盘价 {row['close']:.2f} 元")
                
    except Exception as e:
        print(f"查询出错: {str(e)}")
        print("\n可能的解决方案：")
        print("1. 确保QMT已连接行情服务器")
        print("2. 下载格力电器的历史数据")
        print("3. 检查网络连接")

# 数据下载函数（可选使用）
def download_data():
    """下载历史数据"""
    try:
        download_history_data("000651.SZ", "1m", "20250730", "20250730")
        print("数据下载完成")
    except:
        print("数据下载失败，请手动在QMT中下载")

"""
使用说明：

1. 在QMT中创建新的Python策略
2. 复制此代码到策略编辑器
3. 点击"运行"执行查询

注意事项：
- 000651.SZ 是格力电器的股票代码
- 2025年7月30日是未来日期，需要等到该日期才有实际数据
- 如果查询历史日期，需要先在QMT中下载相应的历史数据
- 交易时间：9:30-11:30, 13:00-15:00

如果没有数据，请：
1. 在QMT菜单中选择"数据管理"
2. 选择"股票数据下载"
3. 输入000651.SZ，选择1分钟数据
4. 设置时间范围包含目标日期
5. 点击下载
"""
